//
//  SoundVisualizerApp.swift
//  SoundVisualizer
//
//  Created by Augment Agent on 2025-06-24.
//

import SwiftUI

/**
 * Main application entry point for the Sound Visualizer app.
 * 
 * This app creates stunning real-time audio visualizations with:
 * - Modern SwiftUI interface with smooth animations
 * - Multiple visualization modes (waveform, frequency bars, circular spectrum)
 * - Real-time audio processing with 60fps rendering
 * - Beautiful gradient backgrounds and micro-animations
 * - Optimized performance for battery efficiency
 */
@main
struct SoundVisualizerApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
                .preferredColorScheme(.dark) // Default to dark theme for better visualization contrast
        }
    }
}
