//
//  PerformanceMonitor.swift
//  SoundVisualizer
//
//  Created by Augment Agent on 2025-06-24.
//

import Foundation
import UIKit

/**
 * Performance monitoring utility for optimizing audio processing and battery usage.
 * 
 * Features:
 * - CPU usage monitoring
 * - Memory usage tracking
 * - Battery level awareness
 * - Thermal state monitoring
 * - Adaptive performance scaling
 */
class PerformanceMonitor: ObservableObject {
    
    // MARK: - Published Properties
    @Published var cpuUsage: Double = 0.0
    @Published var memoryUsage: Double = 0.0
    @Published var batteryLevel: Float = 1.0
    @Published var thermalState: ProcessInfo.ThermalState = .nominal
    @Published var isLowPowerModeEnabled: Bool = false
    
    // MARK: - Private Properties
    private var monitoringTimer: Timer?
    private let updateInterval: TimeInterval = 2.0 // Monitor every 2 seconds
    
    // Performance thresholds
    private let highCPUThreshold: Double = 0.8
    private let highMemoryThreshold: Double = 0.85
    private let lowBatteryThreshold: Float = 0.2
    
    // MARK: - Initialization
    init() {
        startMonitoring()
        setupNotifications()
    }
    
    deinit {
        stopMonitoring()
        removeNotifications()
    }
    
    // MARK: - Public Methods
    
    /**
     * Returns the recommended update interval based on current performance conditions.
     */
    func getRecommendedUpdateInterval() -> TimeInterval {
        let baseInterval: TimeInterval = 1.0 / 60.0 // 60fps base
        
        var multiplier: Double = 1.0
        
        // Adjust based on CPU usage
        if cpuUsage > highCPUThreshold {
            multiplier *= 1.5
        }
        
        // Adjust based on thermal state
        switch thermalState {
        case .serious, .critical:
            multiplier *= 2.0
        case .fair:
            multiplier *= 1.3
        default:
            break
        }
        
        // Adjust based on battery level
        if batteryLevel < lowBatteryThreshold || isLowPowerModeEnabled {
            multiplier *= 1.8
        }
        
        return baseInterval * multiplier
    }
    
    /**
     * Returns the recommended quality level for audio processing.
     */
    func getRecommendedQualityLevel() -> QualityLevel {
        // High performance conditions
        if cpuUsage < 0.5 && memoryUsage < 0.6 && batteryLevel > 0.5 && 
           thermalState == .nominal && !isLowPowerModeEnabled {
            return .high
        }
        
        // Critical performance conditions
        if cpuUsage > highCPUThreshold || memoryUsage > highMemoryThreshold || 
           thermalState == .critical || batteryLevel < 0.1 {
            return .low
        }
        
        // Default to medium quality
        return .medium
    }
    
    /**
     * Returns whether background processing should be enabled.
     */
    func shouldEnableBackgroundProcessing() -> Bool {
        return batteryLevel > 0.3 && !isLowPowerModeEnabled && thermalState != .critical
    }
    
    // MARK: - Private Methods
    
    private func startMonitoring() {
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: updateInterval, repeats: true) { [weak self] _ in
            self?.updatePerformanceMetrics()
        }
    }
    
    private func stopMonitoring() {
        monitoringTimer?.invalidate()
        monitoringTimer = nil
    }
    
    private func updatePerformanceMetrics() {
        DispatchQueue.global(qos: .utility).async { [weak self] in
            guard let self = self else { return }
            
            let cpu = self.getCurrentCPUUsage()
            let memory = self.getCurrentMemoryUsage()
            let battery = UIDevice.current.batteryLevel
            let thermal = ProcessInfo.processInfo.thermalState
            let lowPowerMode = ProcessInfo.processInfo.isLowPowerModeEnabled
            
            DispatchQueue.main.async {
                self.cpuUsage = cpu
                self.memoryUsage = memory
                self.batteryLevel = battery
                self.thermalState = thermal
                self.isLowPowerModeEnabled = lowPowerMode
            }
        }
    }
    
    private func getCurrentCPUUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / Double(ProcessInfo.processInfo.physicalMemory)
        }
        
        return 0.0
    }
    
    private func getCurrentMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / Double(ProcessInfo.processInfo.physicalMemory)
        }
        
        return 0.0
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(batteryLevelChanged),
            name: UIDevice.batteryLevelDidChangeNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(batteryStateChanged),
            name: UIDevice.batteryStateDidChangeNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(thermalStateChanged),
            name: ProcessInfo.thermalStateDidChangeNotification,
            object: nil
        )
        
        // Enable battery monitoring
        UIDevice.current.isBatteryMonitoringEnabled = true
    }
    
    private func removeNotifications() {
        NotificationCenter.default.removeObserver(self)
        UIDevice.current.isBatteryMonitoringEnabled = false
    }
    
    @objc private func batteryLevelChanged() {
        DispatchQueue.main.async { [weak self] in
            self?.batteryLevel = UIDevice.current.batteryLevel
        }
    }
    
    @objc private func batteryStateChanged() {
        DispatchQueue.main.async { [weak self] in
            self?.isLowPowerModeEnabled = ProcessInfo.processInfo.isLowPowerModeEnabled
        }
    }
    
    @objc private func thermalStateChanged() {
        DispatchQueue.main.async { [weak self] in
            self?.thermalState = ProcessInfo.processInfo.thermalState
        }
    }
}

// MARK: - Quality Level Enum
enum QualityLevel {
    case low
    case medium
    case high
    
    var fftSize: Int {
        switch self {
        case .low: return 512
        case .medium: return 1024
        case .high: return 2048
        }
    }
    
    var updateInterval: TimeInterval {
        switch self {
        case .low: return 1.0 / 30.0    // 30fps
        case .medium: return 1.0 / 45.0  // 45fps
        case .high: return 1.0 / 60.0    // 60fps
        }
    }
    
    var bufferSize: Int {
        switch self {
        case .low: return 2048
        case .medium: return 4096
        case .high: return 8192
        }
    }
    
    var smoothingFactor: Float {
        switch self {
        case .low: return 0.9
        case .medium: return 0.85
        case .high: return 0.8
        }
    }
}
