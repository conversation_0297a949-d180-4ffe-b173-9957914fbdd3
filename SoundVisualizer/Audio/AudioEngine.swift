//
//  AudioEngine.swift
//  SoundVisualizer
//
//  Created by Augment Agent on 2025-06-24.
//

import AVFoundation
import Accelerate
import Combine
import UIKit

/**
 * High-performance audio engine for real-time sound visualization.
 * 
 * Features:
 * - Real-time audio input processing with AVAudioEngine
 * - FFT analysis for frequency spectrum calculation
 * - Optimized performance with 60fps updates
 * - Proper error handling and memory management
 * - Battery-efficient audio processing
 */
@MainActor
class AudioEngine: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isRecording = false
    @Published var audioLevels: [Float] = Array(repeating: 0.0, count: 512)
    @Published var frequencyData: [Float] = Array(repeating: 0.0, count: 256)
    @Published var waveformData: [Float] = Array(repeating: 0.0, count: 1024)
    @Published var averageLevel: Float = 0.0
    @Published var peakLevel: Float = 0.0
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let audioEngine = AVAudioEngine()
    private let inputNode: AVAudioInputNode
    private let audioSession = AVAudioSession.sharedInstance()
    
    // FFT Configuration
    private let fftSize = 1024
    private var fftSetup: FFTSetup?
    private var realBuffer: [Float] = []
    private var imagBuffer: [Float] = []
    private var magnitudes: [Float] = []
    
    // Audio buffer management
    private var audioBuffer: [Float] = []
    private let bufferSize = 4096
    private let updateInterval: TimeInterval = 1.0 / 60.0 // 60 FPS
    
    // Performance optimization
    private var lastUpdateTime: CFTimeInterval = 0
    private let processingQueue = DispatchQueue(label: "audio.processing", qos: .userInteractive)
    private var frameCounter = 0
    private var performanceMonitor = PerformanceMonitor()

    // Battery optimization
    private var isInBackground = false
    private var backgroundUpdateInterval: TimeInterval = 1.0 / 30.0 // Reduce to 30fps in background
    
    // MARK: - Initialization
    init() {
        inputNode = audioEngine.inputNode
        setupFFT()
        setupAudioSession()
        setupBackgroundNotifications()
    }
    
    deinit {
        // Stop audio engine synchronously in deinit
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)

        if let fftSetup = fftSetup {
            vDSP_destroy_fftsetup(fftSetup)
        }
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Public Methods
    
    /**
     * Starts audio recording and real-time processing.
     * Requests microphone permission if needed.
     */
    func startRecording() async {
        do {
            // Request microphone permission
            let permissionGranted = await requestMicrophonePermission()
            guard permissionGranted else {
                await MainActor.run {
                    self.errorMessage = "Microphone permission is required for audio visualization."
                }
                return
            }

            // Setup audio session first
            setupAudioSession()

            try await setupAudioEngine()
            
            await MainActor.run {
                self.isRecording = true
                self.errorMessage = nil
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to start audio recording: \(error.localizedDescription)"
                self.isRecording = false
            }
        }
    }
    
    /**
     * Stops audio recording and processing.
     */
    func stopRecording() {
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        
        Task { @MainActor in
            isRecording = false
            // Reset visualization data
            audioLevels = Array(repeating: 0.0, count: 512)
            frequencyData = Array(repeating: 0.0, count: 256)
            waveformData = Array(repeating: 0.0, count: 1024)
            averageLevel = 0.0
            peakLevel = 0.0
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * Sets up the FFT configuration for frequency analysis.
     */
    private func setupFFT() {
        let log2n = vDSP_Length(log2(Float(fftSize)))
        fftSetup = vDSP_create_fftsetup(log2n, FFTRadix(kFFTRadix2))
        
        realBuffer = Array(repeating: 0.0, count: fftSize / 2)
        imagBuffer = Array(repeating: 0.0, count: fftSize / 2)
        magnitudes = Array(repeating: 0.0, count: fftSize / 2)
        audioBuffer = Array(repeating: 0.0, count: bufferSize)
    }
    
    /**
     * Configures the audio session for optimal recording performance.
     */
    private func setupAudioSession() {
        do {
            // Set category for recording with playback capability
            try audioSession.setCategory(.playAndRecord, mode: .measurement, options: [.defaultToSpeaker, .allowBluetooth])

            // Set preferred sample rate (but don't force it)
            try audioSession.setPreferredSampleRate(44100)

            // Set buffer duration for low latency
            try audioSession.setPreferredIOBufferDuration(0.01) // 10ms for stability

            print("Audio session configured successfully")
            print("Sample rate: \(audioSession.sampleRate)")
            print("Input channels: \(audioSession.inputNumberOfChannels)")
            print("Output channels: \(audioSession.outputNumberOfChannels)")

        } catch {
            print("Failed to setup audio session: \(error)")
            DispatchQueue.main.async {
                self.errorMessage = "Audio session setup failed: \(error.localizedDescription)"
            }
        }
    }

    /**
     * Sets up background/foreground notifications for performance optimization.
     */
    private func setupBackgroundNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    /**
     * Requests microphone permission from the user.
     */
    private func requestMicrophonePermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            audioSession.requestRecordPermission { granted in
                continuation.resume(returning: granted)
            }
        }
    }
    
    /**
     * Sets up the audio engine with input tap for real-time processing.
     */
    private func setupAudioEngine() async throws {
        // Get the input node's hardware format
        let inputFormat = inputNode.outputFormat(forBus: 0)

        // Use the hardware format to avoid "Input HW format is invalid" error
        // Only override if the format is clearly incompatible
        let processingFormat: AVAudioFormat
        if inputFormat.sampleRate > 0 && inputFormat.channelCount > 0 {
            // Use hardware format but ensure it's suitable for processing
            if inputFormat.channelCount == 1 {
                processingFormat = inputFormat
            } else {
                // Convert stereo to mono if needed
                processingFormat = AVAudioFormat(standardFormatWithSampleRate: inputFormat.sampleRate, channels: 1)!
            }
        } else {
            // Fallback to standard format
            processingFormat = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 1)!
        }

        // Install tap on input node for real-time audio processing
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: processingFormat) { [weak self] buffer, _ in
            self?.processAudioBuffer(buffer)
        }

        // Activate audio session if not already active
        if !audioSession.isOtherAudioPlaying {
            try audioSession.setActive(true)
        }

        try audioEngine.start()
    }
    
    /**
     * Processes incoming audio buffer for visualization data.
     * Optimized for 60fps performance with minimal CPU usage.
     */
    private func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
        guard let channelData = buffer.floatChannelData?[0] else { return }

        let currentTime = CACurrentMediaTime()
        let adaptiveUpdateInterval = isInBackground ? backgroundUpdateInterval : performanceMonitor.getRecommendedUpdateInterval()
        guard currentTime - lastUpdateTime >= adaptiveUpdateInterval else { return }
        lastUpdateTime = currentTime

        // Performance monitoring
        frameCounter += 1
        if frameCounter % 60 == 0 { // Check every 60 frames
            let qualityLevel = performanceMonitor.getRecommendedQualityLevel()
            adaptProcessingQuality(to: qualityLevel)
        }
        
        processingQueue.async { [weak self] in
            guard let self = self else { return }
            
            let frameCount = Int(buffer.frameLength)
            let samples = Array(UnsafeBufferPointer(start: channelData, count: frameCount))
            
            // Update audio buffer for waveform
            self.updateAudioBuffer(with: samples)
            
            // Calculate audio levels
            let levels = self.calculateAudioLevels(from: samples)
            
            // Perform FFT for frequency analysis
            let frequencies = self.performFFT(on: samples)
            
            // Update UI on main thread
            Task { @MainActor in
                self.audioLevels = levels.audioLevels
                self.frequencyData = frequencies
                self.waveformData = Array(self.audioBuffer.suffix(1024))
                self.averageLevel = levels.average
                self.peakLevel = levels.peak
            }
        }
    }
    
    /**
     * Updates the circular audio buffer for waveform visualization.
     */
    private func updateAudioBuffer(with samples: [Float]) {
        let startIndex = max(0, audioBuffer.count - samples.count)
        audioBuffer.removeFirst(min(samples.count, audioBuffer.count))
        audioBuffer.append(contentsOf: samples)
        
        // Ensure buffer doesn't exceed maximum size
        if audioBuffer.count > bufferSize {
            audioBuffer.removeFirst(audioBuffer.count - bufferSize)
        }
    }
    
    /**
     * Calculates audio levels for visualization.
     */
    private func calculateAudioLevels(from samples: [Float]) -> (audioLevels: [Float], average: Float, peak: Float) {
        let levelCount = 512
        let samplesPerLevel = max(1, samples.count / levelCount)
        var levels: [Float] = []
        
        var totalLevel: Float = 0
        var peakLevel: Float = 0
        
        for i in 0..<levelCount {
            let startIndex = i * samplesPerLevel
            let endIndex = min(startIndex + samplesPerLevel, samples.count)
            
            if startIndex < samples.count {
                let levelSamples = Array(samples[startIndex..<endIndex])
                let rms = sqrt(levelSamples.map { $0 * $0 }.reduce(0, +) / Float(levelSamples.count))
                let dbLevel = 20 * log10(max(rms, 0.000001)) // Avoid log(0)
                let normalizedLevel = max(0, (dbLevel + 60) / 60) // Normalize -60dB to 0dB range
                
                levels.append(normalizedLevel)
                totalLevel += normalizedLevel
                peakLevel = max(peakLevel, normalizedLevel)
            } else {
                levels.append(0)
            }
        }
        
        let averageLevel = totalLevel / Float(levelCount)
        return (levels, averageLevel, peakLevel)
    }
    
    /**
     * Performs FFT analysis on audio samples for frequency visualization.
     */
    private func performFFT(on samples: [Float]) -> [Float] {
        guard let fftSetup = fftSetup, samples.count >= fftSize else {
            return Array(repeating: 0.0, count: 256)
        }
        
        // Prepare input data (take last fftSize samples)
        let inputSamples = Array(samples.suffix(fftSize))
        
        // Apply windowing function (Hann window) to reduce spectral leakage
        var windowedSamples = [Float](repeating: 0.0, count: fftSize)
        for i in 0..<fftSize {
            let window = 0.5 * (1.0 - cos(2.0 * Float.pi * Float(i) / Float(fftSize - 1)))
            windowedSamples[i] = inputSamples[i] * window
        }
        
        // Prepare complex input for FFT
        var realInput = [Float](repeating: 0.0, count: fftSize / 2)
        var imagInput = [Float](repeating: 0.0, count: fftSize / 2)
        
        // Interleave real samples for complex FFT input
        for i in 0..<fftSize / 2 {
            realInput[i] = windowedSamples[i * 2]
            imagInput[i] = i * 2 + 1 < fftSize ? windowedSamples[i * 2 + 1] : 0
        }
        
        // Perform FFT
        var splitComplex = DSPSplitComplex(realp: &realInput, imagp: &imagInput)
        vDSP_fft_zip(fftSetup, &splitComplex, 1, vDSP_Length(log2(Float(fftSize / 2))), FFTDirection(FFT_FORWARD))
        
        // Calculate magnitudes
        vDSP_zvmags(&splitComplex, 1, &magnitudes, 1, vDSP_Length(fftSize / 2))
        
        // Convert to dB and normalize for visualization
        var dbMagnitudes = [Float](repeating: 0.0, count: 256)
        for i in 0..<min(256, magnitudes.count) {
            let magnitude = sqrt(magnitudes[i])
            let dbValue = 20 * log10(max(magnitude, 0.000001))
            dbMagnitudes[i] = max(0, (dbValue + 80) / 80) // Normalize -80dB to 0dB range
        }
        
        return dbMagnitudes
    }

    // MARK: - Performance Optimization Methods

    /**
     * Adapts processing quality based on performance conditions.
     */
    private func adaptProcessingQuality(to qualityLevel: QualityLevel) {
        // Update FFT size if needed
        let newFFTSize = qualityLevel.fftSize
        if newFFTSize != fftSize {
            setupFFTWithSize(newFFTSize)
        }

        // Update buffer size
        let newBufferSize = qualityLevel.bufferSize
        if newBufferSize != bufferSize {
            audioBuffer = Array(repeating: 0.0, count: newBufferSize)
        }
    }

    /**
     * Sets up FFT with a specific size for performance optimization.
     */
    private func setupFFTWithSize(_ size: Int) {
        // Clean up existing FFT setup
        if let fftSetup = fftSetup {
            vDSP_destroy_fftsetup(fftSetup)
        }

        // Create new FFT setup
        let log2n = vDSP_Length(log2(Float(size)))
        fftSetup = vDSP_create_fftsetup(log2n, FFTRadix(kFFTRadix2))

        // Update buffers
        realBuffer = Array(repeating: 0.0, count: size / 2)
        imagBuffer = Array(repeating: 0.0, count: size / 2)
        magnitudes = Array(repeating: 0.0, count: size / 2)
    }

    /**
     * Handles app entering background state.
     */
    @objc private func appDidEnterBackground() {
        isInBackground = true
        // Reduce processing intensity in background
        if isRecording && !performanceMonitor.shouldEnableBackgroundProcessing() {
            Task { @MainActor in
                self.stopRecording()
            }
        }
    }

    /**
     * Handles app entering foreground state.
     */
    @objc private func appWillEnterForeground() {
        isInBackground = false
    }
}
