//
//  VisualizationView.swift
//  SoundVisualizer
//
//  Created by Augment Agent on 2025-06-24.
//

import SwiftUI

/**
 * Main visualization coordinator view that switches between different visualization types.
 * 
 * Features:
 * - Smooth transitions between visualization modes
 * - Optimized rendering for 60fps performance
 * - Responsive design for different screen sizes
 * - Beautiful animations and color-reactive visualizations
 */
struct VisualizationView: View {
    @ObservedObject var audioEngine: AudioEngine
    let visualizationType: VisualizationType
    
    // Animation state
    @State private var animationOffset: CGFloat = 0
    @State private var colorPhase: Double = 0
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background with subtle animation
                backgroundPattern(geometry: geometry)
                
                // Main visualization content
                Group {
                    switch visualizationType {
                    case .waveform:
                        WaveformView(audioEngine: audioEngine)
                            .transition(.asymmetric(
                                insertion: .move(edge: .leading).combined(with: .opacity),
                                removal: .move(edge: .trailing).combined(with: .opacity)
                            ))
                    
                    case .frequencyBars:
                        FrequencyBarsView(audioEngine: audioEngine)
                            .transition(.asymmetric(
                                insertion: .move(edge: .bottom).combined(with: .opacity),
                                removal: .move(edge: .top).combined(with: .opacity)
                            ))
                    
                    case .circularSpectrum:
                        CircularSpectrumView(audioEngine: audioEngine)
                            .transition(.asymmetric(
                                insertion: .scale.combined(with: .opacity),
                                removal: .scale(scale: 0.8).combined(with: .opacity)
                            ))
                    }
                }
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: visualizationType)
                
                // Overlay effects when not recording
                if !audioEngine.isRecording {
                    placeholderOverlay(geometry: geometry)
                }
            }
        }
        .onAppear {
            startBackgroundAnimations()
        }
    }
    
    // MARK: - Background Pattern
    private func backgroundPattern(geometry: GeometryProxy) -> some View {
        ZStack {
            // Animated grid pattern
            Path { path in
                let spacing: CGFloat = 30
                let width = geometry.size.width
                let height = geometry.size.height
                
                // Vertical lines
                for x in stride(from: 0, through: width, by: spacing) {
                    path.move(to: CGPoint(x: x + animationOffset, y: 0))
                    path.addLine(to: CGPoint(x: x + animationOffset, y: height))
                }
                
                // Horizontal lines
                for y in stride(from: 0, through: height, by: spacing) {
                    path.move(to: CGPoint(x: 0, y: y + animationOffset))
                    path.addLine(to: CGPoint(x: width, y: y + animationOffset))
                }
            }
            .stroke(Color.white.opacity(0.05), lineWidth: 0.5)
            .clipped()
            
            // Radial gradient overlay
            RadialGradient(
                colors: [
                    Color.clear,
                    Color.cyan.opacity(0.1),
                    Color.purple.opacity(0.05),
                    Color.clear
                ],
                center: UnitPoint(x: 0.5, y: 0.5),
                startRadius: 0,
                endRadius: max(geometry.size.width, geometry.size.height) * 0.8
            )
            .rotationEffect(.degrees(colorPhase))
        }
    }
    
    // MARK: - Placeholder Overlay
    private func placeholderOverlay(geometry: GeometryProxy) -> some View {
        VStack(spacing: 20) {
            // Animated icon
            Image(systemName: visualizationType.icon)
                .font(.system(size: 60, weight: .ultraLight))
                .foregroundStyle(
                    LinearGradient(
                        colors: [Color.cyan.opacity(0.6), Color.purple.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .scaleEffect(1.0 + sin(colorPhase * 0.02) * 0.1)
            
            // Instructional text
            VStack(spacing: 8) {
                Text("Tap the microphone to start")
                    .font(.headline)
                    .foregroundColor(.white.opacity(0.8))
                
                Text("Experience \(visualizationType.displayName.lowercased()) visualization")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // Animated dots
            HStack(spacing: 8) {
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(Color.cyan.opacity(0.6))
                        .frame(width: 8, height: 8)
                        .scaleEffect(1.0 + sin(colorPhase * 0.03 + Double(index) * 0.5) * 0.5)
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black.opacity(0.3))
    }
    
    // MARK: - Animation Methods
    private func startBackgroundAnimations() {
        // Grid animation
        withAnimation(.linear(duration: 20.0).repeatForever(autoreverses: false)) {
            animationOffset = 30
        }
        
        // Color phase animation
        withAnimation(.linear(duration: 15.0).repeatForever(autoreverses: false)) {
            colorPhase = 360
        }
    }
}

// MARK: - Preview
#Preview {
    VisualizationView(
        audioEngine: AudioEngine(),
        visualizationType: .waveform
    )
    .frame(height: 400)
    .background(Color.black)
}
