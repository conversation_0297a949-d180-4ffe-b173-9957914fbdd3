//
//  ContentView.swift
//  SoundVisualizer
//
//  Created by Augment Agent on 2025-06-24.
//

import SwiftUI

/**
 * Main content view for the Sound Visualizer app.
 * 
 * Features:
 * - Stunning gradient backgrounds with dark theme
 * - Smooth animations and micro-interactions
 * - Responsive layout for different screen sizes
 * - Modern SwiftUI design patterns
 * - Visualization mode switching with beautiful transitions
 */
struct ContentView: View {
    @StateObject private var audioEngine = AudioEngine()
    @State private var selectedVisualization: VisualizationType = .waveform
    @State private var showingSettings = false
    @State private var isAnimating = false
    
    // Animation and UI state
    @State private var pulseScale: CGFloat = 1.0
    @State private var rotationAngle: Double = 0
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Dynamic gradient background
                backgroundGradient
                    .ignoresSafeArea()
                    .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: isAnimating)
                
                VStack(spacing: 0) {
                    // Header with title and controls
                    headerView
                        .padding(.horizontal, 20)
                        .padding(.top, 10)
                    
                    // Main visualization area
                    visualizationContainer(geometry: geometry)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                    
                    // Bottom controls
                    bottomControls
                        .padding(.horizontal, 20)
                        .padding(.bottom, geometry.safeAreaInsets.bottom + 20)
                }
            }
        }
        .preferredColorScheme(.dark)
        .onAppear {
            startAnimations()
        }
        .alert("Error", isPresented: .constant(audioEngine.errorMessage != nil)) {
            Button("OK") {
                audioEngine.errorMessage = nil
            }
        } message: {
            Text(audioEngine.errorMessage ?? "")
        }
    }
    
    // MARK: - Background Gradient
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.15),
                Color(red: 0.1, green: 0.05, blue: 0.2),
                Color(red: 0.15, green: 0.1, blue: 0.25),
                Color(red: 0.08, green: 0.15, blue: 0.3)
            ],
            startPoint: isAnimating ? .topLeading : .bottomTrailing,
            endPoint: isAnimating ? .bottomTrailing : .topLeading
        )
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Sound Visualizer")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.white, Color.cyan.opacity(0.8)],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                
                Text(audioEngine.isRecording ? "Listening..." : "Tap to start")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Settings button
            Button(action: { showingSettings.toggle() }) {
                Image(systemName: "gearshape.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(.ultraThinMaterial)
                            .overlay(
                                Circle()
                                    .stroke(Color.cyan.opacity(0.3), lineWidth: 1)
                            )
                    )
                    .scaleEffect(showingSettings ? 1.1 : 1.0)
                    .rotationEffect(.degrees(rotationAngle))
            }
            .buttonStyle(ScaleButtonStyle())
        }
    }
    
    // MARK: - Visualization Container
    private func visualizationContainer(geometry: GeometryProxy) -> some View {
        VStack(spacing: 20) {
            // Visualization mode selector
            visualizationSelector
                .padding(.horizontal, 20)
            
            // Main visualization view
            VisualizationView(
                audioEngine: audioEngine,
                visualizationType: selectedVisualization
            )
            .frame(maxWidth: .infinity)
            .frame(height: geometry.size.height * 0.5)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(
                                LinearGradient(
                                    colors: [Color.cyan.opacity(0.3), Color.purple.opacity(0.3)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
            )
            .padding(.horizontal, 20)
            .scaleEffect(audioEngine.isRecording ? 1.0 : 0.95)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: audioEngine.isRecording)
        }
    }
    
    // MARK: - Visualization Selector
    private var visualizationSelector: some View {
        HStack(spacing: 12) {
            ForEach(VisualizationType.allCases, id: \.self) { type in
                Button(action: {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        selectedVisualization = type
                    }
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: type.icon)
                            .font(.system(size: 16, weight: .medium))
                        
                        Text(type.displayName)
                            .font(.system(size: 14, weight: .medium))
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(selectedVisualization == type ? 
                                  LinearGradient(colors: [Color.cyan, Color.purple], startPoint: .leading, endPoint: .trailing) :
                                  LinearGradient(colors: [Color.clear], startPoint: .leading, endPoint: .trailing)
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                            )
                    )
                    .foregroundColor(selectedVisualization == type ? .white : .secondary)
                }
                .buttonStyle(ScaleButtonStyle())
            }
        }
        .padding(.horizontal, 4)
    }
    
    // MARK: - Bottom Controls
    private var bottomControls: some View {
        VStack(spacing: 20) {
            // Audio level indicator
            if audioEngine.isRecording {
                audioLevelIndicator
            }
            
            // Main record button
            recordButton
        }
    }
    
    // MARK: - Audio Level Indicator
    private var audioLevelIndicator: some View {
        VStack(spacing: 8) {
            Text("Audio Level")
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack(spacing: 4) {
                ForEach(0..<20, id: \.self) { index in
                    RoundedRectangle(cornerRadius: 2)
                        .fill(
                            audioEngine.averageLevel > Float(index) / 20.0 ?
                            LinearGradient(colors: [.green, .yellow, .red], startPoint: .leading, endPoint: .trailing) :
                            LinearGradient(colors: [Color.white.opacity(0.1)], startPoint: .leading, endPoint: .trailing)
                        )
                        .frame(width: 8, height: 20)
                        .scaleEffect(y: audioEngine.averageLevel > Float(index) / 20.0 ? 1.0 : 0.3)
                        .animation(.easeInOut(duration: 0.1), value: audioEngine.averageLevel)
                }
            }
        }
        .transition(.scale.combined(with: .opacity))
    }
    
    // MARK: - Record Button
    private var recordButton: some View {
        Button(action: {
            if audioEngine.isRecording {
                audioEngine.stopRecording()
            } else {
                Task {
                    await audioEngine.startRecording()
                }
            }
        }) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: audioEngine.isRecording ? 
                            [Color.red, Color.orange] : 
                            [Color.cyan, Color.purple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                    .scaleEffect(pulseScale)
                    .overlay(
                        Circle()
                            .stroke(Color.white.opacity(0.3), lineWidth: 2)
                    )
                
                Image(systemName: audioEngine.isRecording ? "stop.fill" : "mic.fill")
                    .font(.title)
                    .foregroundColor(.white)
                    .scaleEffect(audioEngine.isRecording ? 0.8 : 1.0)
            }
        }
        .buttonStyle(ScaleButtonStyle())
        .disabled(audioEngine.errorMessage != nil)
    }
    
    // MARK: - Helper Methods
    private func startAnimations() {
        withAnimation(.easeInOut(duration: 3.0).repeatForever(autoreverses: true)) {
            isAnimating = true
        }
        
        withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
            pulseScale = 1.1
        }
        
        withAnimation(.linear(duration: 10.0).repeatForever(autoreverses: false)) {
            rotationAngle = 360
        }
    }
}

// MARK: - Visualization Type Enum
enum VisualizationType: String, CaseIterable {
    case waveform = "waveform"
    case frequencyBars = "frequencyBars"
    case circularSpectrum = "circularSpectrum"
    
    var displayName: String {
        switch self {
        case .waveform: return "Waveform"
        case .frequencyBars: return "Frequency"
        case .circularSpectrum: return "Circular"
        }
    }
    
    var icon: String {
        switch self {
        case .waveform: return "waveform"
        case .frequencyBars: return "chart.bar.fill"
        case .circularSpectrum: return "circle.grid.cross.fill"
        }
    }
}

// MARK: - Custom Button Style
struct ScaleButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Preview
#Preview {
    ContentView()
}
