//
//  FrequencyBarsView.swift
//  SoundVisualizer
//
//  Created by Augment Agent on 2025-06-24.
//

import SwiftUI

/**
 * High-performance frequency bars visualization with smooth 60fps rendering.
 * 
 * Features:
 * - Real-time frequency spectrum display with smooth animations
 * - Color-reactive bars based on frequency intensity
 * - Optimized rendering using SwiftUI Canvas for 60fps performance
 * - Beautiful gradient effects and smooth bar transitions
 * - Responsive design with dynamic bar count based on screen size
 */
struct FrequencyBarsView: View {
    @ObservedObject var audioEngine: AudioEngine
    
    // Animation and visual state
    @State private var animationPhase: Double = 0
    @State private var barHeights: [CGFloat] = []
    @State private var previousBarHeights: [CGFloat] = []
    
    // Configuration
    private let maxBars = 64
    private let barSpacing: CGFloat = 2
    private let smoothingFactor: Float = 0.85
    private let minBarHeight: CGFloat = 2
    
    var body: some View {
        GeometryReader { geometry in
            Canvas { context, size in
                drawFrequencyBars(context: context, size: size)
            }
            .onReceive(Timer.publish(every: 1/60.0, on: .main, in: .common).autoconnect()) { _ in
                updateBarHeights(geometry: geometry)
            }
        }
        .background(Color.clear)
        .clipped()
        .onAppear {
            initializeBarHeights()
        }
    }
    
    // MARK: - Initialize Bar Heights
    private func initializeBarHeights() {
        barHeights = Array(repeating: minBarHeight, count: maxBars)
        previousBarHeights = Array(repeating: minBarHeight, count: maxBars)
    }
    
    // MARK: - Update Bar Heights
    private func updateBarHeights(geometry: GeometryProxy) {
        let frequencyData = audioEngine.frequencyData
        guard !frequencyData.isEmpty else { return }
        
        let barCount = min(maxBars, Int(geometry.size.width / (8 + barSpacing)))
        let dataPerBar = max(1, frequencyData.count / barCount)
        
        var newHeights: [CGFloat] = []
        
        for i in 0..<barCount {
            let startIndex = i * dataPerBar
            let endIndex = min(startIndex + dataPerBar, frequencyData.count)
            
            // Calculate average frequency for this bar
            var sum: Float = 0
            for j in startIndex..<endIndex {
                sum += frequencyData[j]
            }
            let average = sum / Float(endIndex - startIndex)
            
            // Apply logarithmic scaling for better visual representation
            let logValue = log10(max(average * 10 + 1, 1.1)) / log10(11)
            let targetHeight = max(CGFloat(logValue) * geometry.size.height * 0.8, minBarHeight)
            
            // Smooth transition
            let currentHeight = i < barHeights.count ? barHeights[i] : minBarHeight
            let smoothedHeight = currentHeight * CGFloat(smoothingFactor) + targetHeight * CGFloat(1 - smoothingFactor)
            
            newHeights.append(smoothedHeight)
        }
        
        // Update with animation
        withAnimation(.linear(duration: 0.016)) {
            previousBarHeights = barHeights
            barHeights = newHeights
            animationPhase += 0.05
        }
    }
    
    // MARK: - Draw Frequency Bars
    private func drawFrequencyBars(context: GraphicsContext, size: CGSize) {
        guard !barHeights.isEmpty else { return }
        
        let barCount = barHeights.count
        let totalSpacing = CGFloat(barCount - 1) * barSpacing
        let availableWidth = size.width - totalSpacing
        let barWidth = availableWidth / CGFloat(barCount)
        
        for (index, height) in barHeights.enumerated() {
            let x = CGFloat(index) * (barWidth + barSpacing)
            let y = size.height - height
            
            // Draw main bar
            drawBar(
                context: context,
                x: x,
                y: y,
                width: barWidth,
                height: height,
                index: index,
                totalBars: barCount
            )
            
            // Draw reflection
            drawBarReflection(
                context: context,
                x: x,
                y: size.height,
                width: barWidth,
                height: height * 0.3,
                index: index,
                totalBars: barCount
            )
        }
        
        // Draw frequency labels
        drawFrequencyLabels(context: context, size: size, barCount: barCount, barWidth: barWidth)
    }
    
    // MARK: - Draw Individual Bar
    private func drawBar(context: GraphicsContext, x: CGFloat, y: CGFloat, width: CGFloat, height: CGFloat, index: Int, totalBars: Int) {
        let rect = CGRect(x: x, y: y, width: width, height: height)
        let cornerRadius = width * 0.2
        
        // Create rounded rectangle path
        let path = Path(roundedRect: rect, cornerRadius: cornerRadius)
        
        // Calculate color based on frequency and intensity
        let normalizedIndex = Double(index) / Double(totalBars)
        let intensity = min(height / 200, 1.0)
        
        let colors = calculateBarColors(normalizedIndex: normalizedIndex, intensity: intensity)
        
        // Create gradient
        let gradient = Gradient(colors: colors)

        // Fill the bar
        context.fill(
            path,
            with: .linearGradient(
                gradient,
                startPoint: CGPoint(x: x, y: y + height),
                endPoint: CGPoint(x: x, y: y)
            )
        )
        
        // Add glow effect for high-intensity bars
        if intensity > 0.6 {
            let glowPath = path
            context.fill(
                glowPath,
                with: .color(colors.last?.opacity(0.3) ?? Color.clear)
            )
            var blurContext = context
            blurContext.addFilter(.blur(radius: intensity * 4))
        }
        
        // Add subtle border
        context.stroke(
            path,
            with: .color(Color.white.opacity(0.1)),
            style: StrokeStyle(lineWidth: 0.5)
        )
    }
    
    // MARK: - Draw Bar Reflection
    private func drawBarReflection(context: GraphicsContext, x: CGFloat, y: CGFloat, width: CGFloat, height: CGFloat, index: Int, totalBars: Int) {
        let rect = CGRect(x: x, y: y, width: width, height: height)
        let cornerRadius = width * 0.2
        
        let path = Path(roundedRect: rect, cornerRadius: cornerRadius)
        
        // Calculate reflection colors (dimmed)
        let normalizedIndex = Double(index) / Double(totalBars)
        let intensity = min(height / 60, 1.0) // Dimmer reflection
        
        let baseColors = calculateBarColors(normalizedIndex: normalizedIndex, intensity: intensity)
        let reflectionColors = baseColors.map { $0.opacity(0.2) }
        
        // Create reflection gradient (inverted)
        let gradient = Gradient(colors: reflectionColors.reversed())

        context.fill(
            path,
            with: .linearGradient(
                gradient,
                startPoint: CGPoint(x: x, y: y),
                endPoint: CGPoint(x: x, y: y + height)
            )
        )
    }
    
    // MARK: - Calculate Bar Colors
    private func calculateBarColors(normalizedIndex: Double, intensity: CGFloat) -> [Color] {
        let hue = normalizedIndex * 0.8 + animationPhase * 0.1 // Cycle through spectrum
        let saturation = 0.8 + intensity * 0.2
        let brightness = 0.6 + intensity * 0.4
        
        let baseColor = Color(hue: hue.truncatingRemainder(dividingBy: 1.0), saturation: saturation, brightness: brightness)
        
        // Create gradient colors
        let darkColor = baseColor.opacity(0.3)
        let midColor = baseColor.opacity(0.7)
        let brightColor = baseColor
        
        if intensity > 0.8 {
            return [darkColor, midColor, brightColor, Color.white.opacity(0.8)]
        } else if intensity > 0.5 {
            return [darkColor, midColor, brightColor]
        } else {
            return [darkColor, midColor]
        }
    }
    
    // MARK: - Draw Frequency Labels
    private func drawFrequencyLabels(context: GraphicsContext, size: CGSize, barCount: Int, barWidth: CGFloat) {
        let labelCount = min(5, barCount / 8) // Show a few frequency labels
        
        for i in 0..<labelCount {
            let barIndex = i * (barCount / labelCount)
            let x = CGFloat(barIndex) * (barWidth + barSpacing) + barWidth / 2
            let frequency = calculateFrequency(barIndex: barIndex, totalBars: barCount)
            
            let text = Text(formatFrequency(frequency))
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(.white.opacity(0.6))
            
            context.draw(
                text,
                at: CGPoint(x: x, y: size.height - 15),
                anchor: .center
            )
        }
    }
    
    // MARK: - Helper Methods
    private func calculateFrequency(barIndex: Int, totalBars: Int) -> Double {
        let nyquistFrequency = 22050.0 // Half of 44.1kHz sample rate
        let frequencyPerBar = nyquistFrequency / Double(totalBars)
        return Double(barIndex) * frequencyPerBar
    }
    
    private func formatFrequency(_ frequency: Double) -> String {
        if frequency >= 1000 {
            return String(format: "%.1fk", frequency / 1000)
        } else {
            return String(format: "%.0f", frequency)
        }
    }
}

// MARK: - Preview
#Preview {
    FrequencyBarsView(audioEngine: AudioEngine())
        .frame(height: 300)
        .background(Color.black)
}
