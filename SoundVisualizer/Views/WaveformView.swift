//
//  WaveformView.swift
//  SoundVisualizer
//
//  Created by Augment Agent on 2025-06-24.
//

import SwiftUI

/**
 * High-performance waveform visualization with smooth 60fps rendering.
 * 
 * Features:
 * - Real-time waveform display with smooth interpolation
 * - Color-reactive visualization based on audio intensity
 * - Optimized drawing using SwiftUI Canvas for 60fps performance
 * - Beautiful gradient effects and animations
 * - Responsive design for different screen sizes
 */
struct WaveformView: View {
    @ObservedObject var audioEngine: AudioEngine
    
    // Animation and visual state
    @State private var animationPhase: Double = 0
    @State private var intensityMultiplier: CGFloat = 1.0
    
    // Performance optimization
    private let maxPoints = 512
    private let smoothingFactor: Float = 0.8
    
    var body: some View {
        GeometryReader { geometry in
            Canvas { context, size in
                drawWaveform(context: context, size: size)
            }
            .onReceive(Timer.publish(every: 1/60.0, on: .main, in: .common).autoconnect()) { _ in
                updateAnimations()
            }
        }
        .background(Color.clear)
        .clipped()
    }
    
    // MARK: - Waveform Drawing
    private func drawWaveform(context: GraphicsContext, size: CGSize) {
        let waveformData = audioEngine.waveformData
        guard !waveformData.isEmpty else { return }
        
        let width = size.width
        let height = size.height
        let centerY = height / 2
        
        // Calculate points for the waveform
        let points = calculateWaveformPoints(data: waveformData, width: width, height: height)
        
        // Draw multiple layers for depth effect
        drawWaveformLayers(context: context, points: points, centerY: centerY, width: width, height: height)
        
        // Draw center line
        drawCenterLine(context: context, width: width, centerY: centerY)
        
        // Draw glow effects
        if audioEngine.averageLevel > 0.1 {
            drawGlowEffects(context: context, points: points, centerY: centerY)
        }
    }
    
    // MARK: - Calculate Waveform Points
    private func calculateWaveformPoints(data: [Float], width: CGFloat, height: CGFloat) -> [CGPoint] {
        let pointCount = min(maxPoints, Int(width / 2)) // Optimize point count based on width
        let dataCount = data.count
        
        guard dataCount > 0 else { return [] }
        
        var points: [CGPoint] = []
        let stepX = width / CGFloat(pointCount - 1)
        
        for i in 0..<pointCount {
            let x = CGFloat(i) * stepX
            
            // Sample data with interpolation for smooth visualization
            let dataIndex = Float(i) * Float(dataCount - 1) / Float(pointCount - 1)
            let lowerIndex = Int(floor(dataIndex))
            let upperIndex = min(lowerIndex + 1, dataCount - 1)
            let fraction = dataIndex - Float(lowerIndex)
            
            let lowerValue = data[lowerIndex]
            let upperValue = data[upperIndex]
            let interpolatedValue = lowerValue + (upperValue - lowerValue) * fraction
            
            // Apply smoothing and intensity scaling
            let smoothedValue = interpolatedValue * smoothingFactor
            let scaledValue = smoothedValue * Float(intensityMultiplier)
            
            // Convert to screen coordinates
            let amplitude = CGFloat(scaledValue) * height * 0.4
            let y = height / 2 + amplitude * sin(animationPhase * 0.1 + Double(i) * 0.02)
            
            points.append(CGPoint(x: x, y: y))
        }
        
        return points
    }
    
    // MARK: - Draw Waveform Layers
    private func drawWaveformLayers(context: GraphicsContext, points: [CGPoint], centerY: CGFloat, width: CGFloat, height: CGFloat) {
        guard points.count > 1 else { return }
        
        // Background layer (subtle)
        drawWaveformLayer(
            context: context,
            points: points,
            colors: [Color.cyan.opacity(0.1), Color.purple.opacity(0.1)],
            lineWidth: 6,
            blur: 4
        )
        
        // Middle layer (medium intensity)
        drawWaveformLayer(
            context: context,
            points: points,
            colors: [Color.cyan.opacity(0.4), Color.purple.opacity(0.4)],
            lineWidth: 3,
            blur: 2
        )
        
        // Foreground layer (sharp)
        drawWaveformLayer(
            context: context,
            points: points,
            colors: [Color.cyan.opacity(0.8), Color.purple.opacity(0.8)],
            lineWidth: 1.5,
            blur: 0
        )
        
        // Intensity-based highlight layer
        if audioEngine.averageLevel > 0.3 {
            drawWaveformLayer(
                context: context,
                points: points,
                colors: [Color.white.opacity(0.6), Color.yellow.opacity(0.4)],
                lineWidth: 1,
                blur: 1
            )
        }
    }
    
    // MARK: - Draw Single Waveform Layer
    private func drawWaveformLayer(context: GraphicsContext, points: [CGPoint], colors: [Color], lineWidth: CGFloat, blur: CGFloat) {
        var path = Path()
        
        // Create smooth curve through points
        if let firstPoint = points.first {
            path.move(to: firstPoint)
            
            for i in 1..<points.count {
                let currentPoint = points[i]
                
                if i == 1 {
                    path.addLine(to: currentPoint)
                } else {
                    let previousPoint = points[i - 1]
                    let controlPoint1 = CGPoint(
                        x: previousPoint.x + (currentPoint.x - previousPoint.x) * 0.3,
                        y: previousPoint.y
                    )
                    let controlPoint2 = CGPoint(
                        x: currentPoint.x - (currentPoint.x - previousPoint.x) * 0.3,
                        y: currentPoint.y
                    )
                    
                    path.addCurve(to: currentPoint, control1: controlPoint1, control2: controlPoint2)
                }
            }
        }
        
        // Apply gradient stroke
        let gradient = Gradient(colors: colors)

        context.stroke(
            path,
            with: .linearGradient(
                gradient,
                startPoint: CGPoint(x: 0, y: 0),
                endPoint: CGPoint(x: context.clipBoundingRect.width, y: 0)
            ),
            style: StrokeStyle(lineWidth: lineWidth, lineCap: .round, lineJoin: .round)
        )
        
        // Apply blur if specified
        if blur > 0 {
            var blurContext = context
            blurContext.addFilter(.blur(radius: blur))
        }
    }
    
    // MARK: - Draw Center Line
    private func drawCenterLine(context: GraphicsContext, width: CGFloat, centerY: CGFloat) {
        let centerPath = Path { path in
            path.move(to: CGPoint(x: 0, y: centerY))
            path.addLine(to: CGPoint(x: width, y: centerY))
        }
        
        context.stroke(
            centerPath,
            with: .color(Color.white.opacity(0.2)),
            style: StrokeStyle(lineWidth: 0.5, lineCap: .round, dash: [5, 5])
        )
    }
    
    // MARK: - Draw Glow Effects
    private func drawGlowEffects(context: GraphicsContext, points: [CGPoint], centerY: CGFloat) {
        // Create glow effect for high-intensity areas
        for (index, point) in points.enumerated() {
            let distance = abs(point.y - centerY)
            let intensity = min(distance / 100, 1.0)
            
            if intensity > 0.5 {
                let glowRadius = intensity * 8
                let glowColor = Color.cyan.opacity(Double(intensity * 0.3))
                
                let glowPath = Path { path in
                    path.addEllipse(in: CGRect(
                        x: point.x - glowRadius,
                        y: point.y - glowRadius,
                        width: glowRadius * 2,
                        height: glowRadius * 2
                    ))
                }
                
                context.fill(glowPath, with: .color(glowColor))
                var glowContext = context
                glowContext.addFilter(.blur(radius: glowRadius))
            }
        }
    }
    
    // MARK: - Animation Updates
    private func updateAnimations() {
        withAnimation(.linear(duration: 0.016)) { // 60fps
            animationPhase += 0.1
            
            // Update intensity multiplier based on audio level
            let targetIntensity = CGFloat(1.0 + audioEngine.averageLevel * 2.0)
            intensityMultiplier = intensityMultiplier * 0.9 + targetIntensity * 0.1
        }
        
        // Reset phase to prevent overflow
        if animationPhase > 1000 {
            animationPhase = 0
        }
    }
}

// MARK: - Preview
#Preview {
    WaveformView(audioEngine: AudioEngine())
        .frame(height: 300)
        .background(Color.black)
}
