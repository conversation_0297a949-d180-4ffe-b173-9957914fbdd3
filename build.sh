#!/bin/bash

# Sound Visualizer Build Script
# This script helps build and test the Sound Visualizer iOS app

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="SoundVisualizer"
SCHEME_NAME="SoundVisualizer"
WORKSPACE_PATH="SoundVisualizer.xcodeproj"

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Sound Visualizer Build Script${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

print_step() {
    echo -e "${YELLOW}➤ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

check_requirements() {
    print_step "Checking build requirements..."
    
    # Check if Xcode is installed
    if ! command -v xcodebuild &> /dev/null; then
        print_error "Xcode command line tools not found. Please install Xcode."
        exit 1
    fi
    
    # Check Xcode version
    XCODE_VERSION=$(xcodebuild -version | head -n 1 | awk '{print $2}')
    print_success "Xcode version: $XCODE_VERSION"
    
    # Check if project exists
    if [ ! -f "$WORKSPACE_PATH/project.pbxproj" ]; then
        print_error "Project file not found: $WORKSPACE_PATH"
        exit 1
    fi
    
    print_success "All requirements met"
    echo ""
}

clean_build() {
    print_step "Cleaning previous builds..."
    
    xcodebuild clean \
        -project "$WORKSPACE_PATH" \
        -scheme "$SCHEME_NAME" \
        -configuration Debug \
        > /dev/null 2>&1
    
    print_success "Clean completed"
    echo ""
}

build_for_simulator() {
    print_step "Building for iOS Simulator..."
    
    xcodebuild build \
        -project "$WORKSPACE_PATH" \
        -scheme "$SCHEME_NAME" \
        -configuration Debug \
        -sdk iphonesimulator \
        -destination 'platform=iOS Simulator,name=iPhone 15 Pro,OS=latest' \
        ONLY_ACTIVE_ARCH=YES \
        CODE_SIGNING_ALLOWED=NO
    
    if [ $? -eq 0 ]; then
        print_success "Simulator build completed successfully"
    else
        print_error "Simulator build failed"
        exit 1
    fi
    echo ""
}

build_for_device() {
    print_step "Building for iOS Device..."
    
    # Note: This requires proper code signing setup
    xcodebuild build \
        -project "$WORKSPACE_PATH" \
        -scheme "$SCHEME_NAME" \
        -configuration Debug \
        -sdk iphoneos \
        -destination 'generic/platform=iOS' \
        ONLY_ACTIVE_ARCH=NO
    
    if [ $? -eq 0 ]; then
        print_success "Device build completed successfully"
    else
        print_error "Device build failed (check code signing)"
        echo "Note: Device builds require proper code signing configuration"
    fi
    echo ""
}

run_tests() {
    print_step "Running unit tests..."
    
    xcodebuild test \
        -project "$WORKSPACE_PATH" \
        -scheme "$SCHEME_NAME" \
        -configuration Debug \
        -sdk iphonesimulator \
        -destination 'platform=iOS Simulator,name=iPhone 15 Pro,OS=latest' \
        > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        print_success "All tests passed"
    else
        print_error "Some tests failed"
        echo "Run 'xcodebuild test ...' manually for detailed output"
    fi
    echo ""
}

analyze_code() {
    print_step "Running static analysis..."
    
    xcodebuild analyze \
        -project "$WORKSPACE_PATH" \
        -scheme "$SCHEME_NAME" \
        -configuration Debug \
        -sdk iphonesimulator \
        > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        print_success "Static analysis completed - no issues found"
    else
        print_error "Static analysis found potential issues"
    fi
    echo ""
}

show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -c, --clean         Clean build artifacts"
    echo "  -s, --simulator     Build for iOS Simulator only"
    echo "  -d, --device        Build for iOS Device only"
    echo "  -t, --test          Run unit tests"
    echo "  -a, --analyze       Run static code analysis"
    echo "  --all               Run all build steps (default)"
    echo ""
    echo "Examples:"
    echo "  $0                  # Run all build steps"
    echo "  $0 --simulator      # Build for simulator only"
    echo "  $0 --clean --test   # Clean and run tests"
    echo ""
}

# Main script logic
main() {
    print_header
    
    # Parse command line arguments
    CLEAN=false
    SIMULATOR=false
    DEVICE=false
    TESTS=false
    ANALYZE=false
    ALL=true
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--clean)
                CLEAN=true
                ALL=false
                shift
                ;;
            -s|--simulator)
                SIMULATOR=true
                ALL=false
                shift
                ;;
            -d|--device)
                DEVICE=true
                ALL=false
                shift
                ;;
            -t|--test)
                TESTS=true
                ALL=false
                shift
                ;;
            -a|--analyze)
                ANALYZE=true
                ALL=false
                shift
                ;;
            --all)
                ALL=true
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Check requirements first
    check_requirements
    
    # Execute requested operations
    if [ "$ALL" = true ]; then
        clean_build
        build_for_simulator
        build_for_device
        run_tests
        analyze_code
    else
        if [ "$CLEAN" = true ]; then
            clean_build
        fi
        
        if [ "$SIMULATOR" = true ]; then
            build_for_simulator
        fi
        
        if [ "$DEVICE" = true ]; then
            build_for_device
        fi
        
        if [ "$TESTS" = true ]; then
            run_tests
        fi
        
        if [ "$ANALYZE" = true ]; then
            analyze_code
        fi
    fi
    
    print_success "Build script completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Open SoundVisualizer.xcodeproj in Xcode"
    echo "2. Select your target device"
    echo "3. Press Cmd+R to run the app"
    echo "4. Grant microphone permission when prompted"
    echo ""
}

# Run main function
main "$@"
